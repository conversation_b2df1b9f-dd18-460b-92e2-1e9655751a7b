import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Code2, 
  Database, 
  BarChart3, 
  Cloud, 
  Users, 
  Lightbulb,
  Brain,
  Zap
} from "lucide-react";

const SkillsSection = () => {
  const skillCategories = [
    {
      icon: Code2,
      title: "Programming Languages",
      skills: [
        { name: "Python", level: 90 },
        { name: "Java", level: 85 },
        { name: "PHP", level: 88 },
        { name: "SQL", level: 95 },
        { name: "JavaScript", level: 82 }
      ]
    },
    {
      icon: Database,
      title: "Database & Analytics",
      skills: [
        { name: "MySQL", level: 92 },
        { name: "PostgreSQL", level: 85 },
        { name: "Power BI", level: 88 },
        { name: "Tableau", level: 85 },
        { name: "Excel (Advanced)", level: 95 }
      ]
    },
    {
      icon: Cloud,
      title: "Cloud & DevOps",
      skills: [
        { name: "<PERSON><PERSON> (EC2, S3)", level: 80 },
        { name: "Google Cloud", level: 75 },
        { name: "Git/GitHub", level: 90 },
        { name: "cPanel/Hostinger", level: 85 }
      ]
    },
    {
      icon: BarChart3,
      title: "Data Engineering",
      skills: [
        { name: "ETL Pipelines", level: 88 },
        { name: "Data Warehousing", level: 85 },
        { name: "Pandas/NumPy", level: 90 },
        { name: "Machine Learning", level: 80 },
        { name: "TensorFlow", level: 75 }
      ]
    }
  ];

  const softSkills = [
    { icon: Users, title: "Leadership", description: "Team Training & System Adoption" },
    { icon: Lightbulb, title: "Innovation", description: "Entrepreneurial Mindset & Strategic Thinking" },
    { icon: Brain, title: "Problem-Solving", description: "Critical Thinking & Process Improvement" },
    { icon: Zap, title: "Communication", description: "Client Engagement & Stakeholder Management" }
  ];

  return (
    <section className="py-20 px-6 bg-muted/30 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
      <div className="absolute bottom-10 right-10 w-40 h-40 bg-gradient-secondary rounded-full opacity-10 blur-3xl animate-float" style={{ animationDelay: '3s' }}></div>

      <div className="container max-w-6xl mx-auto relative z-10">
        <div className="text-center mb-16 space-y-4">
          <Badge variant="secondary" className="text-sm px-4 py-2 bg-primary/10 text-primary border-primary/20">
            Technical Expertise
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold">
            <span className="text-foreground">Skills &</span>
            <br />
            <span className="bg-gradient-primary bg-clip-text text-transparent">Technologies</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Comprehensive expertise across data engineering, analytics, and software development
          </p>
        </div>

        {/* Technical Skills */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {skillCategories.map((category, index) => (
            <Card 
              key={index}
              className="group bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-card transform hover:-translate-y-2"
            >
              <div className="p-8 space-y-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-primary/10 rounded-lg border border-primary/20 group-hover:bg-primary/20 transition-colors">
                    <category.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {category.title}
                  </h3>
                </div>

                <div className="space-y-4">
                  {category.skills.map((skill, skillIndex) => (
                    <div key={skillIndex} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-foreground font-medium">{skill.name}</span>
                        <span className="text-accent font-semibold">{skill.level}%</span>
                      </div>
                      <Progress 
                        value={skill.level} 
                        className="h-2 bg-muted/50"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Soft Skills */}
        <div className="space-y-8">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-foreground mb-2">Core Competencies</h3>
            <p className="text-muted-foreground">Essential skills that drive project success</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {softSkills.map((skill, index) => (
              <Card 
                key={index}
                className="group bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-glow transform hover:-translate-y-2 text-center"
              >
                <div className="p-6 space-y-4">
                  <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <skill.icon className="w-8 h-8 text-primary-foreground" />
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-lg font-bold text-foreground group-hover:text-primary transition-colors">
                      {skill.title}
                    </h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {skill.description}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Languages */}
        <Card className="mt-16 bg-gradient-card backdrop-blur-xl border-border/30">
          <div className="p-8">
            <h3 className="text-2xl font-bold text-foreground mb-6 text-center">Languages</h3>
            <div className="flex justify-center gap-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">Shona</div>
                <div className="text-sm text-muted-foreground">Native</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">English</div>
                <div className="text-sm text-muted-foreground">Fluent</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary mb-1">Russian</div>
                <div className="text-sm text-muted-foreground">Conversational</div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </section>
  );
};

export default SkillsSection;