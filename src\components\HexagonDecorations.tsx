const HexagonDecorations = () => {
  return (
    <>
      {/* Floating hexagon shapes */}
      <div className="absolute top-20 left-10 w-16 h-16 opacity-20 animate-float" style={{ animationDelay: '0s' }}>
        <div className="w-full h-full bg-gradient-primary transform rotate-45 rounded-lg"></div>
      </div>
      
      <div className="absolute top-40 right-20 w-12 h-12 opacity-30 animate-float" style={{ animationDelay: '2s' }}>
        <div className="w-full h-full bg-gradient-secondary transform rotate-12 rounded-md"></div>
      </div>
      
      <div className="absolute bottom-32 left-1/4 w-10 h-10 opacity-25 animate-float" style={{ animationDelay: '4s' }}>
        <div className="w-full h-full bg-accent/50 transform -rotate-45 rounded-sm"></div>
      </div>
      
      <div className="absolute bottom-20 right-1/3 w-14 h-14 opacity-20 animate-float" style={{ animationDelay: '1s' }}>
        <div className="w-full h-full bg-gradient-primary transform rotate-30 rounded-lg"></div>
      </div>
      
      <div className="absolute top-1/2 left-20 w-8 h-8 opacity-30 animate-float" style={{ animationDelay: '3s' }}>
        <div className="w-full h-full bg-gradient-secondary transform -rotate-12 rounded"></div>
      </div>
      
      <div className="absolute top-1/3 right-10 w-6 h-6 opacity-40 animate-float" style={{ animationDelay: '5s' }}>
        <div className="w-full h-full bg-accent/60 transform rotate-60 rounded-sm"></div>
      </div>
    </>
  );
};

export default HexagonDecorations;
