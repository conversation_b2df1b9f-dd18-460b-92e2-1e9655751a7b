import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Building, Calendar, MapPin } from "lucide-react";

const ExperienceSection = () => {
  const experiences = [
    {
      title: "Construction General Manager",
      company: "Martcosy Construction PVT LTD",
      location: "Ruwa, Zimbabwe",
      period: "08/2018 – Present",
      type: "Full-time",
      description: "Spearheaded the development and implementation of a custom Construction Management System to digitize project tracking, employee scheduling, material inventory, payroll, and document control.",
      achievements: [
        "Designed and deployed data-driven modules for BOQ automation and cost estimation",
        "Integrated smart analytics and reporting tools for data-informed decision-making",
        "Managed end-to-end delivery while embedding digital tools for operational efficiency",
        "Provided strategic leadership in digital transformation initiatives"
      ],
      technologies: ["PHP", "MySQL", "Cloud Dashboards", "Data Analytics"]
    },
    {
      title: "Data Entry Clerk - Intern",
      company: "Organization for Public Health Interventions and Development",
      location: "Harare, Zimbabwe",
      period: "08/2023 – 12/2023",
      type: "Internship",
      description: "Accurately entered and updated large volumes of patient and program data into electronic health record (EHR) systems.",
      achievements: [
        "Maintained high data quality and compliance with M&E standards",
        "Supported monthly and quarterly data reconciliation processes",
        "Collaborated with M&E officers to resolve data discrepancies",
        "Handled sensitive health information with strict confidentiality protocols"
      ],
      technologies: ["DHIS2", "OpenMRS", "Data Verification", "Health Records"]
    }
  ];

  return (
    <section className="py-20 px-6 bg-background relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-20 right-10 w-40 h-40 bg-gradient-secondary rounded-full opacity-10 blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 blur-3xl"></div>

      <div className="container max-w-6xl mx-auto relative z-10">
        <div className="text-center mb-16 space-y-4">
          <Badge variant="secondary" className="text-sm px-4 py-2 bg-primary/10 text-primary border-primary/20">
            Professional Journey
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold">
            <span className="text-foreground">Professional</span>
            <br />
            <span className="bg-gradient-primary bg-clip-text text-transparent">Experience</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Building data-driven solutions across construction management and healthcare analytics
          </p>
        </div>

        <div className="space-y-8">
          {experiences.map((exp, index) => (
            <Card 
              key={index}
              className="group bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-card transform hover:-translate-y-2"
            >
              <div className="p-8">
                <div className="grid lg:grid-cols-12 gap-8 items-start">
                  {/* Timeline indicator */}
                  <div className="lg:col-span-3 space-y-4">
                    <div className="flex items-center gap-2 text-accent font-medium">
                      <Calendar className="w-4 h-4" />
                      <span className="text-sm">{exp.period}</span>
                    </div>
                    <Badge variant="outline" className="border-primary/30 text-primary bg-primary/5">
                      {exp.type}
                    </Badge>
                  </div>

                  {/* Content */}
                  <div className="lg:col-span-9 space-y-6">
                    <div className="space-y-3">
                      <h3 className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                        {exp.title}
                      </h3>
                      <div className="flex flex-wrap items-center gap-4 text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Building className="w-4 h-4 text-accent" />
                          <span className="font-medium">{exp.company}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4 text-accent" />
                          <span>{exp.location}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-foreground leading-relaxed">
                      {exp.description}
                    </p>

                    {/* Key achievements */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-foreground">Key Achievements:</h4>
                      <ul className="space-y-2">
                        {exp.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="flex items-start gap-3 text-muted-foreground">
                            <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                            <span className="leading-relaxed">{achievement}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech, techIndex) => (
                        <Badge 
                          key={techIndex}
                          variant="secondary" 
                          className="bg-muted/50 text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors"
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;