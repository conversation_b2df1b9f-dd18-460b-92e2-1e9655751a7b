import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Github, Linkedin, Mail, MapPin, Globe } from "lucide-react";

const HeroSection = () => {
  return (
    <section className="min-h-screen relative overflow-hidden bg-gradient-hero flex items-center justify-center px-6">
      {/* Floating geometric shapes */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-3xl opacity-20 animate-float shadow-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-secondary rounded-2xl opacity-30 animate-float shadow-glow" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-32 left-1/4 w-20 h-20 bg-primary/20 rounded-full animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-accent/15 rounded-3xl animate-float" style={{ animationDelay: '1s' }}></div>
      </div>

      <div className="container max-w-6xl mx-auto relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="space-y-8 animate-slide-up">
            <div className="space-y-4">
              <Badge variant="secondary" className="text-sm px-4 py-2 bg-primary/10 text-primary border-primary/20">
                Business Intelligence Analyst
              </Badge>
              <h1 className="text-5xl lg:text-7xl font-bold leading-tight">
                <span className="text-foreground">Costa</span>
                <br />
                <span className="bg-gradient-primary bg-clip-text text-transparent">Manesa</span>
              </h1>
              <p className="text-xl text-muted-foreground max-w-lg leading-relaxed">
                Driven Data Engineer & Software Developer building data-driven platforms that solve real-world problems.
              </p>
            </div>

            {/* Quick Info */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-accent" />
                <span>Ruwa, Zimbabwe</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-accent" />
                <span>Available for opportunities</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              <Button size="lg" className="bg-gradient-primary hover:shadow-primary transition-all duration-300 transform hover:scale-105">
                View Projects
              </Button>
              <Button variant="outline" size="lg" className="border-primary/30 hover:bg-primary/10 transition-all duration-300" asChild>
                <a href="/Costa-manesa-FlowCV-Resume-20250722 (1).pdf" download>
                  Download CV
                </a>
              </Button>
            </div>

            {/* Social Links */}
            <div className="flex gap-4">
              <a 
                href="https://github.com/Cosymanes" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-card/50 backdrop-blur-sm rounded-lg border border-border/50 hover:bg-primary/10 hover:border-primary/30 transition-all duration-300 group"
              >
                <Github className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
              </a>
              <a 
                href="https://linkedin.com/in/costa-manesa-2909101a9" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-card/50 backdrop-blur-sm rounded-lg border border-border/50 hover:bg-primary/10 hover:border-primary/30 transition-all duration-300 group"
              >
                <Linkedin className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
              </a>
              <a 
                href="https://www.costa.co.zw" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-3 bg-card/50 backdrop-blur-sm rounded-lg border border-border/50 hover:bg-primary/10 hover:border-primary/30 transition-all duration-300 group"
              >
                <Globe className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
              </a>
            </div>
          </div>

          {/* Right Content - 3D Profile Card */}
          <div className="relative flex justify-center lg:justify-end animate-slide-up" style={{ animationDelay: '0.3s' }}>
            <div className="relative">
              {/* Glow effect */}
              <div className="absolute inset-0 bg-gradient-primary rounded-3xl blur-2xl opacity-20 animate-pulse-glow"></div>
              
              {/* Main card */}
              <div className="relative bg-gradient-card backdrop-blur-xl border border-border/30 rounded-3xl p-8 shadow-float transform hover:scale-105 transition-all duration-500">
                <div className="space-y-6">
                  {/* 3D Profile Picture Frame */}
                  <div className="relative w-32 h-32 mx-auto">
                    {/* Multiple shadow layers for 3D effect */}
                    <div className="absolute inset-0 bg-gradient-primary rounded-2xl transform rotate-3 scale-105 opacity-30 blur-sm"></div>
                    <div className="absolute inset-0 bg-gradient-secondary rounded-2xl transform -rotate-2 scale-102 opacity-20 blur-sm"></div>

                    {/* Main frame */}
                    <div className="relative bg-gradient-to-br from-primary/20 via-transparent to-primary/30 rounded-2xl p-1 shadow-2xl transform hover:scale-110 transition-all duration-500 hover:rotate-1">
                      {/* Inner frame with border */}
                      <div className="bg-gradient-to-br from-background via-card to-background rounded-xl p-1 shadow-inner">
                        {/* Image container */}
                        <div className="relative w-full h-full rounded-lg overflow-hidden shadow-lg">
                          <img
                            src="/costa.jpg"
                            alt="Costa Manesa"
                            className="w-full h-full object-cover transform hover:scale-110 transition-transform duration-700"
                          />
                          {/* Subtle overlay for depth */}
                          <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/10 pointer-events-none"></div>
                        </div>
                      </div>

                      {/* Floating glow effect */}
                      <div className="absolute -inset-2 bg-gradient-primary rounded-2xl opacity-20 blur-xl animate-pulse-glow pointer-events-none"></div>
                    </div>
                  </div>
                  
                  <div className="text-center space-y-3">
                    <h3 className="text-2xl font-bold text-foreground">Costa Manesa</h3>
                    <p className="text-accent font-medium">Business Intelligence Analyst</p>
                    <p className="text-sm text-muted-foreground">Data Engineer & Software Developer</p>
                  </div>

                  {/* Key stats */}
                  <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border/30">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">5+</div>
                      <div className="text-xs text-muted-foreground">Years Experience</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">10+</div>
                      <div className="text-xs text-muted-foreground">Technologies</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-1 h-8 bg-gradient-to-b from-primary to-transparent rounded-full"></div>
      </div>
    </section>
  );
};

export default HeroSection;