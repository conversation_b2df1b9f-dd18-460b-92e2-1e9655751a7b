import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ExternalLink, Github, Calendar, Target, Zap } from "lucide-react";

const ProjectsSection = () => {
  const projects = [
    {
      title: "Smart Road Pulse",
      subtitle: "SmartGuardAssist",
      period: "05/2025 – 07/2025",
      category: "IoT & Machine Learning",
      description: "An intelligent traffic monitoring system designed to improve urban mobility and road safety through real-time data collection, analysis, and automated alerts.",
      features: [
        "Real-Time Traffic Data Collection with IoT integration",
        "Machine Learning anomaly detection for congestion and crashes",
        "Smart Alerts & Notifications via SMS to authorities",
        "Web-based dashboard with traffic heatmaps and system logs",
        "Scalable architecture for smart city infrastructure integration"
      ],
      technologies: ["Machine Learning", "IoT", "Data Analytics", "SMS API", "Web Dashboard", "Python"],
      status: "In Development",
      impact: "Improving urban mobility and road safety"
    },
    {
      title: "Multi-Objective Tender Selection System",
      subtitle: "Predictive Model for Tender Win Rate",
      period: "02/2025 – 05/2025",
      category: "Data Science & Optimization",
      description: "An intelligent tender selection system for Zimbabwe's construction SMEs to support fair, efficient, and data-driven procurement decisions using multi-objective optimization.",
      features: [
        "Multi-Criteria Evaluation Engine with weighted scoring model",
        "Clean, responsive dashboard for tender criteria input",
        "Secure tender database with historical performance data",
        "Automated tender ranking and optimal bidder suggestions",
        "Comprehensive reporting module with PDF/Excel exports"
      ],
      technologies: ["Python", "MCDM (AHP, TOPSIS)", "Machine Learning", "Web Dashboard", "Database Design", "Optimization"],
      status: "Completed",
      impact: "Supporting fair procurement in construction SMEs"
    }
  ];

  return (
    <section className="py-20 px-6 bg-background relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-secondary rounded-full opacity-10 blur-3xl animate-float" style={{ animationDelay: '4s' }}></div>

      <div className="container max-w-6xl mx-auto relative z-10">
        <div className="text-center mb-16 space-y-4">
          <Badge variant="secondary" className="text-sm px-4 py-2 bg-primary/10 text-primary border-primary/20">
            Featured Work
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold">
            <span className="text-foreground">Recent</span>
            <br />
            <span className="bg-gradient-primary bg-clip-text text-transparent">Projects</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Innovative solutions leveraging data science, machine learning, and system optimization
          </p>
        </div>

        <div className="space-y-12">
          {projects.map((project, index) => (
            <Card 
              key={index}
              className="group bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-float transform hover:-translate-y-2 overflow-hidden"
            >
              <div className="p-8 lg:p-12">
                <div className="grid lg:grid-cols-3 gap-8">
                  {/* Project Info */}
                  <div className="lg:col-span-2 space-y-6">
                    <div className="space-y-4">
                      <div className="flex flex-wrap items-center gap-4">
                        <Badge 
                          variant={project.status === "Completed" ? "default" : "secondary"}
                          className={project.status === "Completed" 
                            ? "bg-gradient-primary text-primary-foreground" 
                            : "bg-accent/20 text-accent border-accent/30"
                          }
                        >
                          {project.status}
                        </Badge>
                        <div className="flex items-center gap-2 text-muted-foreground text-sm">
                          <Calendar className="w-4 h-4" />
                          <span>{project.period}</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="text-3xl font-bold text-foreground group-hover:text-primary transition-colors">
                          {project.title}
                        </h3>
                        <p className="text-xl text-accent font-medium">{project.subtitle}</p>
                        <Badge variant="outline" className="border-primary/30 text-primary bg-primary/5">
                          {project.category}
                        </Badge>
                      </div>

                      <p className="text-foreground leading-relaxed text-lg">
                        {project.description}
                      </p>
                    </div>

                    {/* Key Features */}
                    <div className="space-y-4">
                      <h4 className="text-lg font-semibold text-foreground flex items-center gap-2">
                        <Zap className="w-5 h-5 text-accent" />
                        Key Features
                      </h4>
                      <ul className="space-y-3">
                        {project.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start gap-3 text-muted-foreground">
                            <div className="w-2 h-2 bg-gradient-primary rounded-full mt-2 flex-shrink-0"></div>
                            <span className="leading-relaxed">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technologies */}
                    <div className="space-y-3">
                      <h4 className="text-lg font-semibold text-foreground">Technologies Used</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech, techIndex) => (
                          <Badge 
                            key={techIndex}
                            variant="secondary" 
                            className="bg-muted/50 text-muted-foreground hover:bg-primary/10 hover:text-primary transition-colors"
                          >
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Project Impact & Actions */}
                  <div className="space-y-6">
                    <Card className="bg-muted/30 border-border/50 p-6">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <Target className="w-5 h-5 text-accent" />
                          <h4 className="font-semibold text-foreground">Project Impact</h4>
                        </div>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {project.impact}
                        </p>
                      </div>
                    </Card>

                    {/* Action buttons */}
                    <div className="space-y-3">
                      <Button 
                        className="w-full bg-gradient-primary hover:shadow-primary transition-all duration-300"
                        size="lg"
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Project
                      </Button>
                      <Button 
                        variant="outline" 
                        className="w-full border-primary/30 hover:bg-primary/10 transition-all duration-300"
                        size="lg"
                      >
                        <Github className="w-4 h-4 mr-2" />
                        View Code
                      </Button>
                    </div>

                    {/* Additional info */}
                    <div className="space-y-2 text-center">
                      <div className="text-2xl font-bold text-primary">2</div>
                      <div className="text-sm text-muted-foreground">Innovative Projects</div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Call to action */}
        <div className="text-center mt-16">
          <Card className="bg-gradient-card backdrop-blur-xl border-border/30 p-8 inline-block">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-foreground">Interested in collaboration?</h3>
              <p className="text-muted-foreground">Let's discuss how we can build something amazing together</p>
              <Button size="lg" className="bg-gradient-primary hover:shadow-primary transition-all duration-300">
                Get In Touch
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;