@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional 3D Portfolio Design System - Costa Manesa BI Analyst
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Base colors - Professional deep blue theme */
    --background: 220 15% 4%;
    --foreground: 210 20% 95%;

    /* Cards with glassmorphism effect */
    --card: 220 15% 8%;
    --card-foreground: 210 20% 95%;

    --popover: 220 15% 8%;
    --popover-foreground: 210 20% 95%;

    /* Primary - Professional blue */
    --primary: 220 70% 55%;
    --primary-foreground: 210 20% 95%;
    --primary-glow: 220 80% 65%;

    /* Secondary - Sophisticated purple accent */
    --secondary: 260 50% 45%;
    --secondary-foreground: 210 20% 95%;

    /* Muted tones */
    --muted: 220 15% 12%;
    --muted-foreground: 210 10% 70%;

    /* Accent - Bright tech blue */
    --accent: 200 90% 60%;
    --accent-foreground: 220 15% 4%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 210 20% 95%;

    --border: 220 15% 15%;
    --input: 220 15% 12%;
    --ring: 220 70% 55%;

    /* Professional gradients */
    --gradient-primary: linear-gradient(135deg, hsl(220, 70%, 55%), hsl(260, 50%, 45%));
    --gradient-secondary: linear-gradient(135deg, hsl(200, 90%, 60%), hsl(220, 70%, 55%));
    --gradient-hero: linear-gradient(135deg, hsl(220, 15%, 4% / 0.9) 0%, hsl(220, 20%, 8% / 0.8) 50%, hsl(260, 25%, 12% / 0.9) 100%);
    --gradient-card: linear-gradient(135deg, hsl(220, 15%, 8% / 0.8), hsl(220, 20%, 12% / 0.6));

    /* Background images with overlays */
    --bg-hero-tech: url('/4k-tech.jpg');
    --bg-hexagon-pattern: url('/hexagon.jpg');
    --gradient-tech-overlay: linear-gradient(135deg, hsl(220, 15%, 4% / 0.85) 0%, hsl(220, 20%, 8% / 0.75) 50%, hsl(260, 25%, 12% / 0.85) 100%);
    --gradient-hexagon-overlay: linear-gradient(135deg, hsl(220, 15%, 8% / 0.9), hsl(220, 20%, 12% / 0.8));

    /* 3D Effects and shadows */
    --shadow-primary: 0 10px 30px -5px hsl(220, 70%, 55% / 0.3);
    --shadow-card: 0 20px 40px -10px hsl(220, 15%, 4% / 0.5);
    --shadow-float: 0 30px 60px -20px hsl(220, 70%, 55% / 0.2);
    --shadow-glow: 0 0 40px hsl(200, 90%, 60% / 0.3);

    /* Professional spacing and animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom 3D Effects and Animations */
@layer utilities {
  .scale-102 {
    transform: scale(1.02);
  }

  .rotate-3 {
    transform: rotate(3deg);
  }

  .animate-pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slide-up 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
  }

  .shadow-float {
    box-shadow: var(--shadow-float);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-hero {
    background: var(--gradient-hero);
  }

  .bg-gradient-card {
    background: var(--gradient-card);
  }

  .bg-hero-tech {
    background-image: var(--bg-hero-tech), var(--gradient-tech-overlay);
    background-size: cover, cover;
    background-position: center, center;
    background-repeat: no-repeat, no-repeat;
    background-attachment: fixed, fixed;
  }

  /* Mobile optimization for hero background */
  @media (max-width: 768px) {
    .bg-hero-tech {
      background-attachment: scroll, scroll;
      background-size: cover, cover;
    }
  }

  .bg-hexagon-pattern {
    background-image: var(--bg-hexagon-pattern), var(--gradient-hexagon-overlay);
    background-size: 400px 400px, cover;
    background-position: center, center;
    background-repeat: repeat, no-repeat;
  }

  .bg-hexagon-subtle {
    background-image: var(--bg-hexagon-pattern);
    background-size: 200px 200px;
    background-position: center;
    background-repeat: repeat;
    opacity: 0.05;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes slide-up {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}