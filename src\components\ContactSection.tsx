import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Globe, 
  Github, 
  Linkedin, 
  Download,
  User,
  Calendar,
  Heart
} from "lucide-react";

const ContactSection = () => {
  const contactInfo = [
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
      primary: true
    },
    {
      icon: Phone,
      label: "Phone",
      value: "+263 779 023 063",
      href: "tel:+263779023063"
    },
    {
      icon: MapPin,
      label: "Location",
      value: "22141 Inverengus Damafalls, Ruwa",
      href: "https://maps.google.com/?q=Ruwa,Zimbabwe"
    },
    {
      icon: Globe,
      label: "Website",
      value: "www.costa.co.zw",
      href: "https://www.costa.co.zw"
    }
  ];

  const socialLinks = [
    {
      icon: Github,
      label: "GitHub",
      value: "github.com/Cosymanes",
      href: "https://github.com/Cosymanes",
      color: "hover:text-foreground"
    },
    {
      icon: Linkedin,
      label: "LinkedIn",
      value: "costa-manesa-2909101a9",
      href: "https://linkedin.com/in/costa-manesa-2909101a9",
      color: "hover:text-blue-400"
    }
  ];

  const personalInfo = [
    { icon: User, label: "Gender", value: "Male" },
    { icon: Calendar, label: "Date of Birth", value: "01/03/2002" },
    { icon: Heart, label: "Marital Status", value: "Single" }
  ];

  return (
    <section className="py-20 px-6 bg-hexagon-pattern relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 blur-3xl animate-float"></div>
      <div className="absolute bottom-10 left-10 w-40 h-40 bg-gradient-secondary rounded-full opacity-10 blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>

      <div className="container max-w-6xl mx-auto relative z-10">
        <div className="text-center mb-16 space-y-4">
          <Badge variant="secondary" className="text-sm px-4 py-2 bg-primary/10 text-primary border-primary/20">
            Get In Touch
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold">
            <span className="text-foreground">Let's</span>
            <br />
            <span className="text-gradient-primary">Connect</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Ready to collaborate on data-driven solutions and innovative projects
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Information */}
          <div className="space-y-8">
            {/* Primary contact */}
            <Card className="bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-glow">
              <div className="p-8 space-y-6">
                <div className="text-center space-y-4">
                  <div className="w-20 h-20 mx-auto bg-gradient-primary rounded-2xl flex items-center justify-center">
                    <Mail className="w-10 h-10 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-foreground mb-2">Ready to collaborate?</h3>
                    <p className="text-muted-foreground">Drop me a message and let's discuss your next project</p>
                  </div>
                </div>

                <div className="space-y-4">
                  {contactInfo.map((contact, index) => (
                    <a
                      key={index}
                      href={contact.href}
                      target={contact.href.startsWith('http') ? '_blank' : undefined}
                      rel={contact.href.startsWith('http') ? 'noopener noreferrer' : undefined}
                      className="flex items-center gap-4 p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 group"
                    >
                      <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                        <contact.icon className="w-5 h-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm text-muted-foreground">{contact.label}</div>
                        <div className="text-foreground font-medium group-hover:text-primary transition-colors">
                          {contact.value}
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </Card>

            {/* Social links */}
            <Card className="bg-gradient-card backdrop-blur-xl border-border/30">
              <div className="p-8 space-y-6">
                <h3 className="text-xl font-bold text-foreground text-center">Social Profiles</h3>
                <div className="grid gap-4">
                  {socialLinks.map((social, index) => (
                    <a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-4 p-4 rounded-lg border border-border/50 hover:border-primary/30 hover:bg-primary/5 transition-all duration-300 group"
                    >
                      <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
                        <social.icon className="w-5 h-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm text-muted-foreground">{social.label}</div>
                        <div className="text-foreground font-medium group-hover:text-primary transition-colors">
                          {social.value}
                        </div>
                      </div>
                    </a>
                  ))}
                </div>
              </div>
            </Card>
          </div>

          {/* Professional Summary & CTA */}
          <div className="space-y-8">
            {/* Professional summary card */}
            <Card className="bg-gradient-card backdrop-blur-xl border-border/30 hover:border-primary/30 transition-all duration-500 hover:shadow-card">
              <div className="p-8 space-y-6">
                <div className="text-center space-y-4">
                  {/* Small profile picture */}
                  <div className="relative w-20 h-20 mx-auto">
                    <div className="relative bg-gradient-to-br from-primary/20 via-transparent to-primary/30 rounded-xl p-0.5 shadow-lg">
                      <div className="bg-gradient-to-br from-background via-card to-background rounded-lg p-0.5">
                        <div className="relative w-full h-full rounded-md overflow-hidden">
                          <img
                            src="/costa.jpg"
                            alt="Costa Manesa"
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-gradient-to-br from-transparent to-primary/5 pointer-events-none"></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold text-foreground">Costa Manesa</h3>
                  <Badge className="bg-gradient-primary text-primary-foreground">
                    Business Intelligence Analyst
                  </Badge>
                </div>

                <div className="space-y-4 text-center">
                  <p className="text-muted-foreground leading-relaxed">
                    Driven Data Engineer & Software Developer with expertise in building scalable data pipelines, 
                    backend systems, and automation tools. Founder of AIVA ZIM and Musondosi.
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 py-4 border-y border-border/30">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">5+</div>
                      <div className="text-sm text-muted-foreground">Years Experience</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">10+</div>
                      <div className="text-sm text-muted-foreground">Technologies</div>
                    </div>
                  </div>
                </div>

                <Button
                  size="lg"
                  className="w-full bg-gradient-primary hover:shadow-primary transition-all duration-300 transform hover:scale-105"
                  asChild
                >
                  <a href="/Costa-manesa-FlowCV-Resume-20250722 (1).pdf" download>
                    <Download className="w-4 h-4 mr-2" />
                    Download CV
                  </a>
                </Button>
              </div>
            </Card>

            {/* Personal Information */}
            <Card className="bg-gradient-card backdrop-blur-xl border-border/30">
              <div className="p-8 space-y-6">
                <h3 className="text-xl font-bold text-foreground text-center">Personal Information</h3>
                <div className="space-y-4">
                  {personalInfo.map((info, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 rounded-lg bg-muted/20">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <info.icon className="w-4 h-4 text-primary" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm text-muted-foreground">{info.label}</div>
                        <div className="text-foreground font-medium">{info.value}</div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Additional details */}
                <div className="pt-4 border-t border-border/30 space-y-2 text-center">
                  <div className="text-sm text-muted-foreground">Driver's License</div>
                  <div className="text-foreground font-medium">Class 2 - LL05U877</div>
                  <div className="text-sm text-muted-foreground">Passport: GN378371</div>
                </div>
              </div>
            </Card>

            {/* Availability status */}
            <Card className="bg-gradient-primary/10 border-primary/30">
              <div className="p-6 text-center space-y-3">
                <div className="w-3 h-3 bg-green-500 rounded-full mx-auto animate-pulse"></div>
                <div>
                  <div className="text-lg font-bold text-foreground">Available for Opportunities</div>
                  <div className="text-sm text-muted-foreground">Open to full-time, contract, and consulting roles</div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-border/30">
          <p className="text-muted-foreground">
            © 2025 Costa Manesa. Built with passion for data-driven innovation.
          </p>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;