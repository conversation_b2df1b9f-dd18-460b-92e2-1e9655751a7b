<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle with gradient -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" filter="url(#shadow)"/>
  
  <!-- Letter C -->
  <path d="M20.5 10.5C19.5 9.5 18.2 9 16.5 9C13.5 9 11 11.5 11 16C11 20.5 13.5 23 16.5 23C18.2 23 19.5 22.5 20.5 21.5" 
        stroke="white" 
        stroke-width="2.5" 
        stroke-linecap="round" 
        fill="none"/>
  
  <!-- Inner highlight for 3D effect -->
  <circle cx="16" cy="16" r="13" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
</svg>
